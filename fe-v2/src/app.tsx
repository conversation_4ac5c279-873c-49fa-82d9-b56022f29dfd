// 运行时配置
import './polyfills';
import React from 'react';

import { SolanaWalletProvider } from './components/WalletProvider';
import GlobalHeaderInfo from './components/GlobalHeaderInfo';
import { MultisigProvider } from './contexts/MultisigContext';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{ name: string }> {
  return { name: 'Squads Network' };
}

// umi layout 运行时配置
export const layout = () => {
  return {
    title: 'Squads Wallet',
    logo: false,
    menu: {
      locale: false,
    },
    childrenRender: (children: React.ReactNode) => {
      return (
        <>
          <GlobalHeaderInfo />
          {children}
        </>
      );
    },
    headerContentRender: false, // 不显示页面标题
    footerRender: false, // 不显示页脚
    // 确保头部高度和样式正确
    headerHeight: 48,
    // 布局配置
    layout: 'side',
    navTheme: 'light',
    // 固定头部
    fixedHeader: true,
    // 侧边栏宽度
    siderWidth: 208,
  };
};

// 全局包装器，为整个应用提供钱包和多签上下文
export function rootContainer(container: React.ReactElement) {
  return (
    <SolanaWalletProvider>
      <MultisigProvider>
        {container}
      </MultisigProvider>
    </SolanaWalletProvider>
  );
}
