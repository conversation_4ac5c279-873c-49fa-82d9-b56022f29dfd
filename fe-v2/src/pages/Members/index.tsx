import React from 'react';
import { Card, message, Alert } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { Users, User, Shield, RefreshCw } from 'lucide-react';
import { useMultisig } from '@/contexts/MultisigContext';

const MembersPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey } = useWallet();
  const { currentMultisig, loading: multisigLoading, error: multisigError } = useMultisig();

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  const getCurrentUserRole = () => {
    if (!currentMultisig || !publicKey) return null;

    const userMember = currentMultisig.members.find(member =>
      member.key === publicKey.toBase58()
    );

    if (!userMember) return null;

    // 检查是否是创建者
    if (currentMultisig.createKey === publicKey.toBase58()) {
      return 'Creator';
    }

    return 'Member';
  };

  return (
    <>
      {contextHolder}
      <div style={{ padding: '24px' }}>
        <Card>
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '24px' }}>
              <Users size={24} color="#007bff" />
              <h2 style={{ margin: 0, color: '#333' }}>成员管理</h2>
            </div>

            {/* 多签信息显示 */}
            {multisigLoading && (
              <Alert
                message="正在加载多签账户信息..."
                type="info"
                showIcon
                icon={<RefreshCw className="w-4 h-4 animate-spin" />}
                style={{ marginBottom: '16px' }}
              />
            )}

            {multisigError && (
              <Alert
                message="加载多签信息失败"
                description={multisigError}
                type="error"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {!multisigLoading && !multisigError && !currentMultisig && (
              <Alert
                message="未配置多签账户"
                description="请联系管理员配置多签账户地址"
                type="warning"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {currentMultisig && (
              <div style={{ marginTop: '24px' }}>
                <div style={{
                  background: '#f8f9fa',
                  padding: '16px',
                  borderRadius: '8px',
                  marginBottom: '24px'
                }}>
                  <h3 style={{ margin: '0 0 16px 0', display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Shield size={20} />
                    多签配置
                  </h3>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div>
                      <strong>阈值：</strong>
                      {currentMultisig.threshold} / {currentMultisig.members.length}
                    </div>
                    <div>
                      <strong>当前角色：</strong>
                      {getCurrentUserRole() || '非成员'}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 style={{ margin: '0 0 16px 0', display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <User size={20} />
                    成员列表
                  </h3>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    {currentMultisig.members.map((member) => (
                      <div
                        key={member.key}
                        style={{
                          padding: '12px',
                          background: isCurrentUser(member.key) ? '#f0f8ff' : '#fff',
                          border: '1px solid #e1e4e8',
                          borderRadius: '6px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <User size={16} />
                          <code>{formatAddress(member.key, 12)}</code>
                          {member.key === currentMultisig.createKey && (
                            <span style={{
                              background: '#52c41a',
                              color: '#fff',
                              padding: '2px 8px',
                              borderRadius: '12px',
                              fontSize: '12px'
                            }}>
                              创建者
                            </span>
                          )}
                          {isCurrentUser(member.key) && (
                            <span style={{
                              background: '#1890ff',
                              color: '#fff',
                              padding: '2px 8px',
                              borderRadius: '12px',
                              fontSize: '12px'
                            }}>
                              当前用户
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    </>
  );
};

export default MembersPage;