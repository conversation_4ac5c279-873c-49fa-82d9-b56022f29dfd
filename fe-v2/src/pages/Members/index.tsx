import React from 'react';
import { Card, Alert, Typography, Space, Badge } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { TeamOutlined, UserOutlined, SafetyOutlined, LoadingOutlined } from '@ant-design/icons';
import { useMultisig } from '@/contexts/MultisigContext';

const MembersPage: React.FC = () => {
  const { publicKey } = useWallet();
  const { currentMultisig, loading: multisigLoading, error: multisigError } = useMultisig();

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  const getCurrentUserRole = () => {
    if (!currentMultisig || !publicKey) return null;

    const userMember = currentMultisig.members.find(member =>
      member.key === publicKey.toBase58()
    );

    if (!userMember) return null;

    // 检查是否是创建者
    if (currentMultisig.createKey === publicKey.toBase58()) {
      return 'Creator';
    }

    return 'Member';
  };

  const { Title } = Typography;

  return (
    <div style={{ padding: 24 }}>
        <Card>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Space size="middle">
              <TeamOutlined style={{ fontSize: 24, color: '#1890ff' }} />
              <Title level={2} style={{ margin: 0 }}>成员管理</Title>
            </Space>

            {/* 多签信息显示 */}
            {multisigLoading && (
              <Alert
                message="正在加载多签账户信息..."
                type="info"
                showIcon
                icon={<LoadingOutlined />}
              />
            )}

            {multisigError && (
              <Alert
                message="加载多签信息失败"
                description={multisigError}
                type="error"
                showIcon
              />
            )}

            {!multisigLoading && !multisigError && !currentMultisig && (
              <Alert
                message="未配置多签账户"
                description="请联系管理员配置多签账户地址"
                type="warning"
                showIcon
              />
            )}

            {currentMultisig && (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
                  <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <Space>
                      <SafetyOutlined />
                      <Title level={4} style={{ margin: 0 }}>多签配置</Title>
                    </Space>
                    <div>
                      <Space direction="vertical" size="small">
                        <div>
                          <strong>阈值：</strong>
                          {currentMultisig.threshold} / {currentMultisig.members.length}
                        </div>
                        <div>
                          <strong>当前角色：</strong>
                          {getCurrentUserRole() || '非成员'}
                        </div>
                      </Space>
                    </div>
                  </Space>
                </Card>

                <div>
                  <Space style={{ marginBottom: 16 }}>
                    <UserOutlined />
                    <Title level={4} style={{ margin: 0 }}>成员列表</Title>
                  </Space>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    {currentMultisig.members.map((member) => (
                      <Card
                        key={member.key}
                        size="small"
                        style={{
                          backgroundColor: isCurrentUser(member.key) ? '#f0f8ff' : '#fff',
                        }}
                      >
                        <Space>
                          <UserOutlined />
                          <code>{formatAddress(member.key, 12)}</code>
                          {member.key === currentMultisig.createKey && (
                            <Badge count="创建者" style={{ backgroundColor: '#52c41a' }} />
                          )}
                          {isCurrentUser(member.key) && (
                            <Badge count="当前用户" style={{ backgroundColor: '#1890ff' }} />
                          )}
                        </Space>
                      </Card>
                    ))}
                  </Space>
                </div>
              </Space>
            )}
          </Space>
        </Card>
      </div>
  );
};

export default MembersPage;