import React from 'react';
import { Card, Alert, Typography, Space, Badge } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { TeamOutlined, UserOutlined, SafetyOutlined, LoadingOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';

const MembersPage: React.FC = () => {
  const { publicKey } = useWallet();
  const { currentMultisig, loading: multisigLoading, error: multisigError } = useModel('multisig');

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  const getCurrentUserRole = () => {
    if (!currentMultisig || !publicKey) return null;

    const userMember = currentMultisig.members.find(member =>
      member.key === publicKey.toBase58()
    );

    if (!userMember) return null;

    // 检查是否是创建者
    if (currentMultisig.createKey === publicKey.toBase58()) {
      return 'Creator';
    }

    return 'Member';
  };

  const { Title } = Typography;

  return (
    <div>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {currentMultisig && (
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <Space style={{ marginBottom: 16 }}>
                  <UserOutlined />
                  <Title level={4} style={{ margin: 0 }}>成员列表</Title>
                </Space>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  {currentMultisig.members.map((member) => (
                    <Card
                      key={member.key}
                      size="small"
                      style={{
                        backgroundColor: isCurrentUser(member.key) ? '#f0f8ff' : '#fff',
                      }}
                    >
                      <Space>
                        <UserOutlined />
                        <code>{formatAddress(member.key, 12)}</code>
                        {member.key === currentMultisig.createKey && (
                          <Badge count="创建者" style={{ backgroundColor: '#52c41a' }} />
                        )}
                        {isCurrentUser(member.key) && (
                          <Badge count="当前用户" style={{ backgroundColor: '#1890ff' }} />
                        )}
                      </Space>
                    </Card>
                  ))}
                </Space>
              </div>
            </Space>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default MembersPage;