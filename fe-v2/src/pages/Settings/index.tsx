import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Badge, Button, message, Spin, Alert } from 'antd';
import {
  SettingOutlined,
  ReloadOutlined,
  TeamOutlined,
  WalletOutlined,
  KeyOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { Shield } from 'lucide-react';
import { MultisigInfo } from '@/services/types';
import Api from '@/services/api';

const Settings: React.FC = () => {
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadMultisigSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await Api.getMultisigs();
      setMultisigs(result.multisigs);

      if (result.multisigs.length === 0) {
        message.warning('未找到配置的多签账户');
      } else {
        message.success(`成功加载 ${result.multisigs.length} 个多签账户配置`);
      }
    } catch (err: any) {
      console.error('加载多签配置失败:', err);
      setError(err.message || '加载多签配置失败');
      message.error('加载多签配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMultisigSettings();
  }, []);

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label} 已复制到剪贴板`);
    }).catch(() => {
      message.error('复制失败');
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spin size="large" />
        <span className="ml-3 text-lg">加载多签配置中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" type="primary" onClick={loadMultisigSettings}>
            重试
          </Button>
        }
      />
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <SettingOutlined className="text-2xl text-blue-500" />
          <h1 className="text-2xl font-bold m-0">多签设置</h1>
        </div>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={loadMultisigSettings}
        >
          刷新
        </Button>
      </div>

      {multisigs.map((multisig) => (
        <Card key={multisig.address} className="mb-6">
          <Descriptions
            title={
              <div className="flex items-center gap-2">
                <WalletOutlined />
                <span>多签账户信息</span>
              </div>
            }
            bordered
            column={2}
          >
            <Descriptions.Item label="地址">
              <div className="flex items-center gap-2">
                <code>{formatAddress(multisig.address, 12)}</code>
                <Button
                  type="link"
                  size="small"
                  onClick={() => copyToClipboard(multisig.address, '地址')}
                >
                  复制
                </Button>
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="状态">
              <Badge status="processing" text="活跃" />
            </Descriptions.Item>

            <Descriptions.Item label="阈值">
              <div className="flex items-center gap-2">
                <Shield size={16} />
                {multisig.threshold}/{multisig.members.length}
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="交易索引">
              {multisig.transactionIndex}
            </Descriptions.Item>

            <Descriptions.Item label="创建者">
              <div className="flex items-center gap-2">
                <KeyOutlined />
                <code>{formatAddress(multisig.createKey)}</code>
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="外部执行">
              <Badge
                status={multisig.allowExternalExecute ? 'success' : 'default'}
                text={multisig.allowExternalExecute ? '允许' : '禁止'}
              />
            </Descriptions.Item>

            <Descriptions.Item label="金库地址" span={2}>
              <div className="flex items-center gap-2">
                <code>{formatAddress(multisig.vault.address, 12)}</code>
                <Button
                  type="link"
                  size="small"
                  onClick={() => copyToClipboard(multisig.vault.address, '金库地址')}
                >
                  复制
                </Button>
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="SOL余额" span={2}>
              {multisig.vault.balanceSOL.toFixed(6)} SOL
            </Descriptions.Item>

            <Descriptions.Item label="Token资产" span={2}>
              <div className="space-y-2">
                {multisig.vault.tokenAccounts.map((token) => (
                  <div key={token.address} className="flex items-center gap-2">
                    <span>{token.uiAmountString}</span>
                    <code>{formatAddress(token.mint, 6)}</code>
                  </div>
                ))}
                {multisig.vault.tokenAccounts.length === 0 && (
                  <span className="text-gray-500">暂无Token资产</span>
                )}
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="成员列表" span={2}>
              <div className="space-y-2">
                {multisig.members.map((member) => (
                  <div key={member.key} className="flex items-center gap-2">
                    <TeamOutlined />
                    <code>{formatAddress(member.key, 12)}</code>
                    {member.key === multisig.createKey && (
                      <Badge count="创建者" style={{ backgroundColor: '#52c41a' }} />
                    )}
                  </div>
                ))}
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      ))}

      {multisigs.length === 0 && (
        <div className="text-center py-12">
          <InfoCircleOutlined style={{ fontSize: 48 }} className="text-gray-300 mb-4" />
          <p className="text-gray-500">未找到多签账户配置</p>
        </div>
      )}
    </div>
  );
};

export default Settings;
