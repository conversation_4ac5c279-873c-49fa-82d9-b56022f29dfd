import React from 'react'
import { Card, Descriptions, Button, message, Space, Typography, Row, Col } from 'antd'
import { SettingOutlined, ReloadOutlined } from '@ant-design/icons'
import { useMultisig } from '@/contexts/MultisigContext'

const Settings: React.FC = () => {
  const { currentMultisig: multisig, loading, error, refreshMultisigs } = useMultisig()

  const handleRefresh = async () => {
    try {
      await refreshMultisigs(true) // 强制刷新
      message.success('刷新成功')
    } catch (err) {
      message.error('刷新失败')
    }
  }

  const copyToClipboard = (text: string | undefined, label: string) => {
    if (!text) return
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label} 已复制到剪贴板`)
    }).catch(() => {
      message.error('复制失败')
    })
  }

  const { Title } = Typography

  if (error) {
    return (
      <div style={{ padding: 24 }}>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Space size="middle">
              <SettingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
              <Title level={2} style={{ margin: 0 }}>多签设置</Title>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <p style={{ color: '#ff4d4f', margin: 0 }}>加载失败: {error}</p>
            <Button type="primary" onClick={handleRefresh} style={{ marginTop: 16 }}>
              重试
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  if (!multisig && !loading) {
    return (
      <div style={{ padding: 24 }}>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Space size="middle">
              <SettingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
              <Title level={2} style={{ margin: 0 }}>多签设置</Title>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <p style={{ color: '#999', margin: 0 }}>未找到多签账户配置</p>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Space size="middle">
            <SettingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Title level={2} style={{ margin: 0 }}>多签设置</Title>
          </Space>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Col>
      </Row>

      <Card loading={loading}>
        <Descriptions bordered>
          <Descriptions.Item label="交易索引" span={3}>
            {multisig?.transactionIndex}
          </Descriptions.Item>
          <Descriptions.Item label="Members" span={3}>
            <div>{multisig?.members.length}</div>
          </Descriptions.Item>
          <Descriptions.Item label="Threshold" span={3}>
            <Space>{multisig?.threshold}/{multisig?.members?.length ? multisig?.members?.length - 1 : 0}</Space>
          </Descriptions.Item>
          <Descriptions.Item label="Squad Vault" span={3}>
            <Space>
              <code>{multisig?.vault.address}</code>
              <Button type="link" size="small" onClick={() => copyToClipboard(multisig?.vault?.address, 'Squad Vault')}>复制</Button>
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="Multisig Account" span={3}>
            <Space>
              <code>{multisig?.address}</code>
              <Button type="link" size="small" onClick={() => copyToClipboard(multisig?.address, 'Multisig Account')}>复制</Button>
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  )
}

export default Settings
