import React, { useState, useEffect } from 'react'
import { Card, Descriptions, Button, message, Space } from 'antd'
import Api from '@/services/api'
import { MultisigInfo } from '@/services/types'

const Settings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [multisig, setMultisig] = useState<MultisigInfo>()

  const loadMultisigSettings = async () => {
    setLoading(true)
    const result = await Api.getMultisigs()
    setMultisig(result.multisigs[0])
    if (result.multisigs.length === 0) message.warning('未找到配置的多签账户')
    setLoading(false)
  }

  useEffect(() => {
    loadMultisigSettings()
  }, [])

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label} 已复制到剪贴板`)
    }).catch(() => {
      message.error('复制失败')
    })
  }

  return (
    <div>
      <Card key={multisig?.address} style={{ marginBottom: 24 }} loading={loading}>
        <Descriptions bordered>
          <Descriptions.Item label="交易索引" span={3}>
            {multisig?.transactionIndex}
          </Descriptions.Item>
          <Descriptions.Item label="Members" span={3}>
            <div>{multisig?.members.length}</div>
          </Descriptions.Item>
          <Descriptions.Item label="Threshold" span={3}>
            <Space>{multisig?.threshold}/{multisig?.members?.length - 1}</Space>
          </Descriptions.Item>
          <Descriptions.Item label="Squad Vault" span={3}>
            <Space>
              <code>{multisig?.vault.address}</code>
              <Button type="link" size="small" onClick={() => copyToClipboard(multisig?.vault?.address, 'Squad Vault')}>复制</Button>
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="Multisig Account" span={3}>
            <Space>
              <code>{multisig?.address}</code>
              <Button type="link" size="small" onClick={() => copyToClipboard(multisig?.address, 'Multisig Account')}>复制</Button>
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  )
}

export default Settings
