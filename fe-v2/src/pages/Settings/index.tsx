import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Badge, Button, message, Spin, Alert, Typography, Space, Row, Col } from 'antd';
import {
  SettingOutlined,
  ReloadOutlined,
  TeamOutlined,
  WalletOutlined,
  KeyOutlined,
  InfoCircleOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { MultisigInfo } from '@/services/types';
import Api from '@/services/api';

const Settings: React.FC = () => {
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadMultisigSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await Api.getMultisigs();
      setMultisigs(result.multisigs);

      if (result.multisigs.length === 0) {
        message.warning('未找到配置的多签账户');
      } else {
        message.success(`成功加载 ${result.multisigs.length} 个多签账户配置`);
      }
    } catch (err: any) {
      console.error('加载多签配置失败:', err);
      setError(err.message || '加载多签配置失败');
      message.error('加载多签配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMultisigSettings();
  }, []);

  const formatAddress = (address: string, length: number = 8) => {
    if (!address) return '';
    return `${address.substring(0, length)}...${address.substring(address.length - 4)}`;
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label} 已复制到剪贴板`);
    }).catch(() => {
      message.error('复制失败');
    });
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, fontSize: 16 }}>加载多签配置中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" type="primary" onClick={loadMultisigSettings}>
            重试
          </Button>
        }
      />
    );
  }

  const { Title } = Typography;

  return (
    <div style={{ padding: 24 }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Space size="middle">
            <SettingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Title level={2} style={{ margin: 0 }}>多签设置</Title>
          </Space>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={loadMultisigSettings}
          >
            刷新
          </Button>
        </Col>
      </Row>

      {multisigs.map((multisig) => (
        <Card key={multisig.address} style={{ marginBottom: 24 }}>
          <Descriptions
            title={
              <Space>
                <WalletOutlined />
                <span>多签账户信息</span>
              </Space>
            }
            bordered
            column={2}
          >
            <Descriptions.Item label="地址">
              <Space>
                <code>{formatAddress(multisig.address, 12)}</code>
                <Button
                  type="link"
                  size="small"
                  onClick={() => copyToClipboard(multisig.address, '地址')}
                >
                  复制
                </Button>
              </Space>
            </Descriptions.Item>

            <Descriptions.Item label="状态">
              <Badge status="processing" text="活跃" />
            </Descriptions.Item>

            <Descriptions.Item label="阈值">
              <Space>
                <SafetyOutlined />
                {multisig.threshold}/{multisig.members.length}
              </Space>
            </Descriptions.Item>

            <Descriptions.Item label="交易索引">
              {multisig.transactionIndex}
            </Descriptions.Item>

            <Descriptions.Item label="创建者">
              <Space>
                <KeyOutlined />
                <code>{formatAddress(multisig.createKey)}</code>
              </Space>
            </Descriptions.Item>

            <Descriptions.Item label="外部执行">
              <Badge
                status={multisig.allowExternalExecute ? 'success' : 'default'}
                text={multisig.allowExternalExecute ? '允许' : '禁止'}
              />
            </Descriptions.Item>

            <Descriptions.Item label="金库地址" span={2}>
              <Space>
                <code>{formatAddress(multisig.vault.address, 12)}</code>
                <Button
                  type="link"
                  size="small"
                  onClick={() => copyToClipboard(multisig.vault.address, '金库地址')}
                >
                  复制
                </Button>
              </Space>
            </Descriptions.Item>

            <Descriptions.Item label="SOL余额" span={2}>
              {multisig.vault.balanceSOL.toFixed(6)} SOL
            </Descriptions.Item>

            <Descriptions.Item label="Token资产" span={2}>
              <div>
                {multisig.vault.tokenAccounts.map((token) => (
                  <div key={token.address} style={{ marginBottom: 8 }}>
                    <Space>
                      <span>{token.uiAmountString}</span>
                      <code>{formatAddress(token.mint, 6)}</code>
                    </Space>
                  </div>
                ))}
                {multisig.vault.tokenAccounts.length === 0 && (
                  <span style={{ color: '#999' }}>暂无Token资产</span>
                )}
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="成员列表" span={2}>
              <div>
                {multisig.members.map((member) => (
                  <div key={member.key} style={{ marginBottom: 8 }}>
                    <Space>
                      <TeamOutlined />
                      <code>{formatAddress(member.key, 12)}</code>
                      {member.key === multisig.createKey && (
                        <Badge count="创建者" style={{ backgroundColor: '#52c41a' }} />
                      )}
                    </Space>
                  </div>
                ))}
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      ))}

      {multisigs.length === 0 && (
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <InfoCircleOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <p style={{ color: '#999', margin: 0 }}>未找到多签账户配置</p>
        </div>
      )}
    </div>
  );
};

export default Settings;
