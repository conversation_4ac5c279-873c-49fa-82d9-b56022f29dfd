import React, { useState, useEffect } from 'react'
import { Card, Descriptions, Button, message, Spin, Alert, Space } from 'antd'
import Api from '@/services/api'
import { MultisigInfo } from '@/services/types'

const Settings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([])

  const loadMultisigSettings = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await Api.getMultisigs()
      setMultisigs(result.multisigs)

      if (result.multisigs.length === 0) {
        message.warning('未找到配置的多签账户')
      } else {
        message.success(`成功加载 ${result.multisigs.length} 个多签账户配置`)
      }
    } catch (err: any) {
      console.error('加载多签配置失败:', err)
      setError(err.message || '加载多签配置失败')
      message.error('加载多签配置失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadMultisigSettings()
  }, [])

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label} 已复制到剪贴板`)
    }).catch(() => {
      message.error('复制失败')
    })
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, fontSize: 16 }}>加载多签配置中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" type="primary" onClick={loadMultisigSettings}>
            重试
          </Button>
        }
      />
    )
  }
  return (
    <div>
      {multisigs.map((multisig) => (
        <Card key={multisig.address} style={{ marginBottom: 24 }}>
          <Descriptions bordered>
            <Descriptions.Item label="交易索引" span={3}>
              {multisig.transactionIndex}
            </Descriptions.Item>
            <Descriptions.Item label="Members" span={3}>
              <div>{multisig.members.length}</div>
            </Descriptions.Item>
            <Descriptions.Item label="Threshold" span={3}>
              <Space>{multisig.threshold}/{multisig.members.length - 1}</Space>
            </Descriptions.Item>
            <Descriptions.Item label="Squad Vault" span={3}>
              <Space>
                <code>{multisig.vault.address}</code>
                <Button type="link" size="small" onClick={() => copyToClipboard(multisig.vault.address, 'Squad Vault')}>复制</Button>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Multisig Account" span={3}>
              <Space>
                <code>{multisig.address}</code>
                <Button type="link" size="small" onClick={() => copyToClipboard(multisig.address, 'Multisig Account')}>复制</Button>
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      ))}
    </div>
  )
}

export default Settings
