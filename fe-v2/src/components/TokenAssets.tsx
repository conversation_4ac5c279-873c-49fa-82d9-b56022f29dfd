import React, { useMemo } from 'react';
import { TokenAccount } from '@/services/types';
import { useMultisig } from '@/contexts/MultisigContext';

interface TokenAssetsProps {
  multisigAddress?: string;
  onError?: (error: string) => void;
}

const TokenAssets: React.FC<TokenAssetsProps> = ({ multisigAddress, onError }) => {
  const { multisigs, loading, error } = useMultisig();

  // 计算 Token 资产
  const tokens = useMemo(() => {
    if (multisigAddress) {
      // 获取指定多签的 Token 资产
      const multisig = multisigs.find(m => m.address === multisigAddress);
      return multisig?.vault.tokenAccounts || [];
    } else {
      // 获取所有多签的 Token 资产
      return multisigs.flatMap(m => m.vault.tokenAccounts);
    }
  }, [multisigs, multisigAddress]);

  // 处理错误
  React.useEffect(() => {
    if (error) {
      onError?.(error);
    }
  }, [error, onError]);

  const formatTokenAmount = (token: TokenAccount) => {
    return `${token.uiAmountString} ${getTokenSymbol(token.mint)}`;
  };

  const getTokenSymbol = (mint: string) => {
    // 常见 Token 的符号映射
    const tokenSymbols: { [key: string]: string } = {
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
      'So11111111111111111111111111111111111111112': 'SOL',
      'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So': 'mSOL',
    };
    return tokenSymbols[mint] || `${mint.substring(0, 4)}...${mint.substring(mint.length - 4)}`;
  };

  const formatAddress = (address: string) => {
    return `${address.substring(0, 8)}...${address.substring(address.length - 4)}`;
  };

  if (loading) {
    return <div>加载中...</div>;
  }

  if (tokens.length === 0) {
    return <div>暂无 Token 资产</div>;
  }

  return (
    <div className="space-y-4">
      {tokens.map((token) => (
        <div
          key={token.address}
          className="p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
        >
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">{formatTokenAmount(token)}</div>
              <div className="text-sm text-gray-500">
                Token: {formatAddress(token.mint)}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">
                账户: {formatAddress(token.address)}
              </div>
              <div className="text-xs text-gray-400">
                小数位数: {token.decimals}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TokenAssets;
