import React from 'react';
import { Spin } from 'antd';
import { WalletOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useMultisig } from '@/contexts/MultisigContext';

export const WalletInfo: React.FC = () => {
  const { connected, publicKey } = useWallet();
  const { multisigs, loading } = useMultisig();

  // 计算所有多签账户的总 SOL 余额
  const totalBalance = multisigs.reduce((total, multisig) => {
    return total + (multisig.vault.balanceSOL || 0);
  }, 0);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      height: '100%',
      paddingRight: '16px'
    }}>
      {connected && publicKey && (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '4px 8px',
          backgroundColor: '#f0f2f5',
          borderRadius: '6px'
        }}>
          <WalletOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
          <div>
            <div style={{ fontSize: '12px', color: '#666', lineHeight: 1.2 }}>
              {formatAddress(publicKey.toBase58())}
            </div>
            <div style={{ fontSize: '13px', fontWeight: '600', lineHeight: 1.2, color: '#52c41a' }}>
              {loading ? (
                <Spin size="small" />
              ) : (
                `${totalBalance.toFixed(4)} SOL`
              )}
            </div>
          </div>
        </div>
      )}
      <WalletMultiButton
        style={{
          height: '36px',
          fontSize: '14px',
          borderRadius: '6px'
        }}
      />
    </div>
  );
};