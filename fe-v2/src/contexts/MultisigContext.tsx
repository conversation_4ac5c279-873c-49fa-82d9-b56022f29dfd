import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import Api from '@/services/api/index';
import { MultisigInfo } from '@/services/types';

interface MultisigContextType {
  multisigs: MultisigInfo[];
  currentMultisig: MultisigInfo | null;
  loading: boolean;
  error: string | null;
  refreshMultisigs: () => Promise<void>;
}

const MultisigContext = createContext<MultisigContextType | undefined>(undefined);

interface MultisigProviderProps {
  children: ReactNode;
}

export const MultisigProvider: React.FC<MultisigProviderProps> = ({ children }) => {
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshMultisigs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await Api.getMultisigs();
      setMultisigs(result.multisigs);
    } catch (err: any) {
      console.error('获取多签信息失败:', err);
      setError(err.message || '获取多签信息失败');
      setMultisigs([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshMultisigs();
  }, []);

  // 当前多签账户（通常是第一个，因为后端配置的是固定的）
  const currentMultisig = multisigs.length > 0 ? multisigs[0] : null;

  const value: MultisigContextType = {
    multisigs,
    currentMultisig,
    loading,
    error,
    refreshMultisigs,
  };

  return (
    <MultisigContext.Provider value={value}>
      {children}
    </MultisigContext.Provider>
  );
};

export const useMultisig = (): MultisigContextType => {
  const context = useContext(MultisigContext);
  if (context === undefined) {
    throw new Error('useMultisig must be used within a MultisigProvider');
  }
  return context;
};
