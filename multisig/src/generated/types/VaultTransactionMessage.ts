/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'
import {
  MultisigCompiledInstruction,
  multisigCompiledInstructionBeet,
} from './MultisigCompiledInstruction'
import {
  MultisigMessageAddressTableLookup,
  multisigMessageAddressTableLookupBeet,
} from './MultisigMessageAddressTableLookup'
export type VaultTransactionMessage = {
  numSigners: number
  numWritableSigners: number
  numWritableNonSigners: number
  accountKeys: web3.PublicKey[]
  instructions: MultisigCompiledInstruction[]
  addressTableLookups: MultisigMessageAddressTableLookup[]
}

/**
 * @category userTypes
 * @category generated
 */
export const vaultTransactionMessageBeet =
  new beet.FixableBeetArgsStruct<VaultTransactionMessage>(
    [
      ['numSigners', beet.u8],
      ['numWritableSigners', beet.u8],
      ['numWritableNonSigners', beet.u8],
      ['accountKeys', beet.array(beetSolana.publicKey)],
      ['instructions', beet.array(multisigCompiledInstructionBeet)],
      [
        'addressTableLookups',
        beet.array(multisigMessageAddressTableLookupBeet),
      ],
    ],
    'VaultTransactionMessage'
  )
