/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'
import { Period, periodBeet } from './Period'
export type MultisigAddSpendingLimitArgs = {
  createKey: web3.PublicKey
  vaultIndex: number
  mint: web3.PublicKey
  amount: beet.bignum
  period: Period
  members: web3.PublicKey[]
  destinations: web3.PublicKey[]
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const multisigAddSpendingLimitArgsBeet =
  new beet.FixableBeetArgsStruct<MultisigAddSpendingLimitArgs>(
    [
      ['createKey', beetSolana.publicKey],
      ['vaultIndex', beet.u8],
      ['mint', beetSolana.publicKey],
      ['amount', beet.u64],
      ['period', periodBeet],
      ['members', beet.array(beetSolana.publicKey)],
      ['destinations', beet.array(beetSolana.publicKey)],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'MultisigAddSpendingLimitArgs'
  )
