/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'
export type MultisigRemoveMemberArgs = {
  oldMember: web3.PublicKey
  memo: beet.COption<string>
}

/**
 * @category userTypes
 * @category generated
 */
export const multisigRemoveMemberArgsBeet =
  new beet.FixableBeetArgsStruct<MultisigRemoveMemberArgs>(
    [
      ['oldMember', beetSolana.publicKey],
      ['memo', beet.coption(beet.utf8String)],
    ],
    'MultisigRemoveMemberArgs'
  )
