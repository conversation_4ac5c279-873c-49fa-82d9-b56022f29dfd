/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'

/**
 * Arguments used to create {@link Batch}
 * @category Accounts
 * @category generated
 */
export type BatchArgs = {
  multisig: web3.PublicKey
  creator: web3.PublicKey
  index: beet.bignum
  bump: number
  vaultIndex: number
  vaultBump: number
  size: number
  executedTransactionIndex: number
}

export const batchDiscriminator = [156, 194, 70, 44, 22, 88, 137, 44]
/**
 * Holds the data for the {@link Batch} Account and provides de/serialization
 * functionality for that data
 *
 * @category Accounts
 * @category generated
 */
export class Batch implements BatchArgs {
  private constructor(
    readonly multisig: web3.<PERSON><PERSON><PERSON>,
    readonly creator: web3.<PERSON><PERSON><PERSON>,
    readonly index: beet.bignum,
    readonly bump: number,
    readonly vaultIndex: number,
    readonly vaultBump: number,
    readonly size: number,
    readonly executedTransactionIndex: number
  ) {}

  /**
   * Creates a {@link Batch} instance from the provided args.
   */
  static fromArgs(args: BatchArgs) {
    return new Batch(
      args.multisig,
      args.creator,
      args.index,
      args.bump,
      args.vaultIndex,
      args.vaultBump,
      args.size,
      args.executedTransactionIndex
    )
  }

  /**
   * Deserializes the {@link Batch} from the data of the provided {@link web3.AccountInfo}.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static fromAccountInfo(
    accountInfo: web3.AccountInfo<Buffer>,
    offset = 0
  ): [Batch, number] {
    return Batch.deserialize(accountInfo.data, offset)
  }

  /**
   * Retrieves the account info from the provided address and deserializes
   * the {@link Batch} from its data.
   *
   * @throws Error if no account info is found at the address or if deserialization fails
   */
  static async fromAccountAddress(
    connection: web3.Connection,
    address: web3.PublicKey,
    commitmentOrConfig?: web3.Commitment | web3.GetAccountInfoConfig
  ): Promise<Batch> {
    const accountInfo = await connection.getAccountInfo(
      address,
      commitmentOrConfig
    )
    if (accountInfo == null) {
      throw new Error(`Unable to find Batch account at ${address}`)
    }
    return Batch.fromAccountInfo(accountInfo, 0)[0]
  }

  /**
   * Provides a {@link web3.Connection.getProgramAccounts} config builder,
   * to fetch accounts matching filters that can be specified via that builder.
   *
   * @param programId - the program that owns the accounts we are filtering
   */
  static gpaBuilder(
    programId: web3.PublicKey = new web3.PublicKey(
      'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf'
    )
  ) {
    return beetSolana.GpaBuilder.fromStruct(programId, batchBeet)
  }

  /**
   * Deserializes the {@link Batch} from the provided data Buffer.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static deserialize(buf: Buffer, offset = 0): [Batch, number] {
    return batchBeet.deserialize(buf, offset)
  }

  /**
   * Serializes the {@link Batch} into a Buffer.
   * @returns a tuple of the created Buffer and the offset up to which the buffer was written to store it.
   */
  serialize(): [Buffer, number] {
    return batchBeet.serialize({
      accountDiscriminator: batchDiscriminator,
      ...this,
    })
  }

  /**
   * Returns the byteSize of a {@link Buffer} holding the serialized data of
   * {@link Batch}
   */
  static get byteSize() {
    return batchBeet.byteSize
  }

  /**
   * Fetches the minimum balance needed to exempt an account holding
   * {@link Batch} data from rent
   *
   * @param connection used to retrieve the rent exemption information
   */
  static async getMinimumBalanceForRentExemption(
    connection: web3.Connection,
    commitment?: web3.Commitment
  ): Promise<number> {
    return connection.getMinimumBalanceForRentExemption(
      Batch.byteSize,
      commitment
    )
  }

  /**
   * Determines if the provided {@link Buffer} has the correct byte size to
   * hold {@link Batch} data.
   */
  static hasCorrectByteSize(buf: Buffer, offset = 0) {
    return buf.byteLength - offset === Batch.byteSize
  }

  /**
   * Returns a readable version of {@link Batch} properties
   * and can be used to convert to JSON and/or logging
   */
  pretty() {
    return {
      multisig: this.multisig.toBase58(),
      creator: this.creator.toBase58(),
      index: (() => {
        const x = <{ toNumber: () => number }>this.index
        if (typeof x.toNumber === 'function') {
          try {
            return x.toNumber()
          } catch (_) {
            return x
          }
        }
        return x
      })(),
      bump: this.bump,
      vaultIndex: this.vaultIndex,
      vaultBump: this.vaultBump,
      size: this.size,
      executedTransactionIndex: this.executedTransactionIndex,
    }
  }
}

/**
 * @category Accounts
 * @category generated
 */
export const batchBeet = new beet.BeetStruct<
  Batch,
  BatchArgs & {
    accountDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['accountDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['multisig', beetSolana.publicKey],
    ['creator', beetSolana.publicKey],
    ['index', beet.u64],
    ['bump', beet.u8],
    ['vaultIndex', beet.u8],
    ['vaultBump', beet.u8],
    ['size', beet.u32],
    ['executedTransactionIndex', beet.u32],
  ],
  Batch.fromArgs,
  'Batch'
)
