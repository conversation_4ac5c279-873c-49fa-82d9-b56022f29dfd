/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'
import { ConfigAction, configActionBeet } from '../types/ConfigAction'

/**
 * Arguments used to create {@link ConfigTransaction}
 * @category Accounts
 * @category generated
 */
export type ConfigTransactionArgs = {
  multisig: web3.PublicKey
  creator: web3.PublicKey
  index: beet.bignum
  bump: number
  actions: ConfigAction[]
}

export const configTransactionDiscriminator = [94, 8, 4, 35, 113, 139, 139, 112]
/**
 * Holds the data for the {@link ConfigTransaction} Account and provides de/serialization
 * functionality for that data
 *
 * @category Accounts
 * @category generated
 */
export class ConfigTransaction implements ConfigTransactionArgs {
  private constructor(
    readonly multisig: web3.<PERSON><PERSON><PERSON>,
    readonly creator: web3.<PERSON><PERSON><PERSON>,
    readonly index: beet.bignum,
    readonly bump: number,
    readonly actions: ConfigAction[]
  ) {}

  /**
   * Creates a {@link ConfigTransaction} instance from the provided args.
   */
  static fromArgs(args: ConfigTransactionArgs) {
    return new ConfigTransaction(
      args.multisig,
      args.creator,
      args.index,
      args.bump,
      args.actions
    )
  }

  /**
   * Deserializes the {@link ConfigTransaction} from the data of the provided {@link web3.AccountInfo}.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static fromAccountInfo(
    accountInfo: web3.AccountInfo<Buffer>,
    offset = 0
  ): [ConfigTransaction, number] {
    return ConfigTransaction.deserialize(accountInfo.data, offset)
  }

  /**
   * Retrieves the account info from the provided address and deserializes
   * the {@link ConfigTransaction} from its data.
   *
   * @throws Error if no account info is found at the address or if deserialization fails
   */
  static async fromAccountAddress(
    connection: web3.Connection,
    address: web3.PublicKey,
    commitmentOrConfig?: web3.Commitment | web3.GetAccountInfoConfig
  ): Promise<ConfigTransaction> {
    const accountInfo = await connection.getAccountInfo(
      address,
      commitmentOrConfig
    )
    if (accountInfo == null) {
      throw new Error(`Unable to find ConfigTransaction account at ${address}`)
    }
    return ConfigTransaction.fromAccountInfo(accountInfo, 0)[0]
  }

  /**
   * Provides a {@link web3.Connection.getProgramAccounts} config builder,
   * to fetch accounts matching filters that can be specified via that builder.
   *
   * @param programId - the program that owns the accounts we are filtering
   */
  static gpaBuilder(
    programId: web3.PublicKey = new web3.PublicKey(
      'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf'
    )
  ) {
    return beetSolana.GpaBuilder.fromStruct(programId, configTransactionBeet)
  }

  /**
   * Deserializes the {@link ConfigTransaction} from the provided data Buffer.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static deserialize(buf: Buffer, offset = 0): [ConfigTransaction, number] {
    return configTransactionBeet.deserialize(buf, offset)
  }

  /**
   * Serializes the {@link ConfigTransaction} into a Buffer.
   * @returns a tuple of the created Buffer and the offset up to which the buffer was written to store it.
   */
  serialize(): [Buffer, number] {
    return configTransactionBeet.serialize({
      accountDiscriminator: configTransactionDiscriminator,
      ...this,
    })
  }

  /**
   * Returns the byteSize of a {@link Buffer} holding the serialized data of
   * {@link ConfigTransaction} for the provided args.
   *
   * @param args need to be provided since the byte size for this account
   * depends on them
   */
  static byteSize(args: ConfigTransactionArgs) {
    const instance = ConfigTransaction.fromArgs(args)
    return configTransactionBeet.toFixedFromValue({
      accountDiscriminator: configTransactionDiscriminator,
      ...instance,
    }).byteSize
  }

  /**
   * Fetches the minimum balance needed to exempt an account holding
   * {@link ConfigTransaction} data from rent
   *
   * @param args need to be provided since the byte size for this account
   * depends on them
   * @param connection used to retrieve the rent exemption information
   */
  static async getMinimumBalanceForRentExemption(
    args: ConfigTransactionArgs,
    connection: web3.Connection,
    commitment?: web3.Commitment
  ): Promise<number> {
    return connection.getMinimumBalanceForRentExemption(
      ConfigTransaction.byteSize(args),
      commitment
    )
  }

  /**
   * Returns a readable version of {@link ConfigTransaction} properties
   * and can be used to convert to JSON and/or logging
   */
  pretty() {
    return {
      multisig: this.multisig.toBase58(),
      creator: this.creator.toBase58(),
      index: (() => {
        const x = <{ toNumber: () => number }>this.index
        if (typeof x.toNumber === 'function') {
          try {
            return x.toNumber()
          } catch (_) {
            return x
          }
        }
        return x
      })(),
      bump: this.bump,
      actions: this.actions,
    }
  }
}

/**
 * @category Accounts
 * @category generated
 */
export const configTransactionBeet = new beet.FixableBeetStruct<
  ConfigTransaction,
  ConfigTransactionArgs & {
    accountDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['accountDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['multisig', beetSolana.publicKey],
    ['creator', beetSolana.publicKey],
    ['index', beet.u64],
    ['bump', beet.u8],
    ['actions', beet.array(configActionBeet)],
  ],
  ConfigTransaction.fromArgs,
  'ConfigTransaction'
)
