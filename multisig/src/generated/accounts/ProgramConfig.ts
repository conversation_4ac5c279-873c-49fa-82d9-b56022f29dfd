/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js'
import * as beet from '@metaplex-foundation/beet'
import * as beetSolana from '@metaplex-foundation/beet-solana'

/**
 * Arguments used to create {@link ProgramConfig}
 * @category Accounts
 * @category generated
 */
export type ProgramConfigArgs = {
  authority: web3.PublicKey
  multisigCreationFee: beet.bignum
  treasury: web3.PublicKey
  reserved: number[] /* size: 64 */
}

export const programConfigDiscriminator = [196, 210, 90, 231, 144, 149, 140, 63]
/**
 * Holds the data for the {@link ProgramConfig} Account and provides de/serialization
 * functionality for that data
 *
 * @category Accounts
 * @category generated
 */
export class ProgramConfig implements ProgramConfigArgs {
  private constructor(
    readonly authority: web3.<PERSON><PERSON>ey,
    readonly multisigCreationFee: beet.bignum,
    readonly treasury: web3.<PERSON>Key,
    readonly reserved: number[] /* size: 64 */
  ) {}

  /**
   * Creates a {@link ProgramConfig} instance from the provided args.
   */
  static fromArgs(args: ProgramConfigArgs) {
    return new ProgramConfig(
      args.authority,
      args.multisigCreationFee,
      args.treasury,
      args.reserved
    )
  }

  /**
   * Deserializes the {@link ProgramConfig} from the data of the provided {@link web3.AccountInfo}.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static fromAccountInfo(
    accountInfo: web3.AccountInfo<Buffer>,
    offset = 0
  ): [ProgramConfig, number] {
    return ProgramConfig.deserialize(accountInfo.data, offset)
  }

  /**
   * Retrieves the account info from the provided address and deserializes
   * the {@link ProgramConfig} from its data.
   *
   * @throws Error if no account info is found at the address or if deserialization fails
   */
  static async fromAccountAddress(
    connection: web3.Connection,
    address: web3.PublicKey,
    commitmentOrConfig?: web3.Commitment | web3.GetAccountInfoConfig
  ): Promise<ProgramConfig> {
    const accountInfo = await connection.getAccountInfo(
      address,
      commitmentOrConfig
    )
    if (accountInfo == null) {
      throw new Error(`Unable to find ProgramConfig account at ${address}`)
    }
    return ProgramConfig.fromAccountInfo(accountInfo, 0)[0]
  }

  /**
   * Provides a {@link web3.Connection.getProgramAccounts} config builder,
   * to fetch accounts matching filters that can be specified via that builder.
   *
   * @param programId - the program that owns the accounts we are filtering
   */
  static gpaBuilder(
    programId: web3.PublicKey = new web3.PublicKey(
      'SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf'
    )
  ) {
    return beetSolana.GpaBuilder.fromStruct(programId, programConfigBeet)
  }

  /**
   * Deserializes the {@link ProgramConfig} from the provided data Buffer.
   * @returns a tuple of the account data and the offset up to which the buffer was read to obtain it.
   */
  static deserialize(buf: Buffer, offset = 0): [ProgramConfig, number] {
    return programConfigBeet.deserialize(buf, offset)
  }

  /**
   * Serializes the {@link ProgramConfig} into a Buffer.
   * @returns a tuple of the created Buffer and the offset up to which the buffer was written to store it.
   */
  serialize(): [Buffer, number] {
    return programConfigBeet.serialize({
      accountDiscriminator: programConfigDiscriminator,
      ...this,
    })
  }

  /**
   * Returns the byteSize of a {@link Buffer} holding the serialized data of
   * {@link ProgramConfig}
   */
  static get byteSize() {
    return programConfigBeet.byteSize
  }

  /**
   * Fetches the minimum balance needed to exempt an account holding
   * {@link ProgramConfig} data from rent
   *
   * @param connection used to retrieve the rent exemption information
   */
  static async getMinimumBalanceForRentExemption(
    connection: web3.Connection,
    commitment?: web3.Commitment
  ): Promise<number> {
    return connection.getMinimumBalanceForRentExemption(
      ProgramConfig.byteSize,
      commitment
    )
  }

  /**
   * Determines if the provided {@link Buffer} has the correct byte size to
   * hold {@link ProgramConfig} data.
   */
  static hasCorrectByteSize(buf: Buffer, offset = 0) {
    return buf.byteLength - offset === ProgramConfig.byteSize
  }

  /**
   * Returns a readable version of {@link ProgramConfig} properties
   * and can be used to convert to JSON and/or logging
   */
  pretty() {
    return {
      authority: this.authority.toBase58(),
      multisigCreationFee: (() => {
        const x = <{ toNumber: () => number }>this.multisigCreationFee
        if (typeof x.toNumber === 'function') {
          try {
            return x.toNumber()
          } catch (_) {
            return x
          }
        }
        return x
      })(),
      treasury: this.treasury.toBase58(),
      reserved: this.reserved,
    }
  }
}

/**
 * @category Accounts
 * @category generated
 */
export const programConfigBeet = new beet.BeetStruct<
  ProgramConfig,
  ProgramConfigArgs & {
    accountDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['accountDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['authority', beetSolana.publicKey],
    ['multisigCreationFee', beet.u64],
    ['treasury', beetSolana.publicKey],
    ['reserved', beet.uniformFixedSizeArray(beet.u8, 64)],
  ],
  ProgramConfig.fromArgs,
  'ProgramConfig'
)
