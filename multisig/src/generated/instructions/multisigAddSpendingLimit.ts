/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  MultisigAddSpendingLimitArgs,
  multisigAddSpendingLimitArgsBeet,
} from '../types/MultisigAddSpendingLimitArgs'

/**
 * @category Instructions
 * @category MultisigAddSpendingLimit
 * @category generated
 */
export type MultisigAddSpendingLimitInstructionArgs = {
  args: MultisigAddSpendingLimitArgs
}
/**
 * @category Instructions
 * @category MultisigAddSpendingLimit
 * @category generated
 */
export const multisigAddSpendingLimitStruct = new beet.FixableBeetArgsStruct<
  MultisigAddSpendingLimitInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', multisigAddSpendingLimitArgsBeet],
  ],
  'MultisigAddSpendingLimitInstructionArgs'
)
/**
 * Accounts required by the _multisigAddSpendingLimit_ instruction
 *
 * @property [] multisig
 * @property [**signer**] configAuthority
 * @property [_writable_] spendingLimit
 * @property [_writable_, **signer**] rentPayer
 * @category Instructions
 * @category MultisigAddSpendingLimit
 * @category generated
 */
export type MultisigAddSpendingLimitInstructionAccounts = {
  multisig: web3.PublicKey
  configAuthority: web3.PublicKey
  spendingLimit: web3.PublicKey
  rentPayer: web3.PublicKey
  systemProgram?: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const multisigAddSpendingLimitInstructionDiscriminator = [
  11, 242, 159, 42, 86, 197, 89, 115,
]

/**
 * Creates a _MultisigAddSpendingLimit_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category MultisigAddSpendingLimit
 * @category generated
 */
export function createMultisigAddSpendingLimitInstruction(
  accounts: MultisigAddSpendingLimitInstructionAccounts,
  args: MultisigAddSpendingLimitInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = multisigAddSpendingLimitStruct.serialize({
    instructionDiscriminator: multisigAddSpendingLimitInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.configAuthority,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.spendingLimit,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.rentPayer,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
