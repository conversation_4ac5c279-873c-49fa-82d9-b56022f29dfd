/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as splToken from '@solana/spl-token'
import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  SpendingLimitUseArgs,
  spendingLimitUseArgsBeet,
} from '../types/SpendingLimitUseArgs'

/**
 * @category Instructions
 * @category SpendingLimitUse
 * @category generated
 */
export type SpendingLimitUseInstructionArgs = {
  args: SpendingLimitUseArgs
}
/**
 * @category Instructions
 * @category SpendingLimitUse
 * @category generated
 */
export const spendingLimitUseStruct = new beet.FixableBeetArgsStruct<
  SpendingLimitUseInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', spendingLimitUseArgsBeet],
  ],
  'SpendingLimitUseInstructionArgs'
)
/**
 * Accounts required by the _spendingLimitUse_ instruction
 *
 * @property [] multisig
 * @property [**signer**] member
 * @property [_writable_] spendingLimit
 * @property [_writable_] vault
 * @property [_writable_] destination
 * @property [] mint (optional)
 * @property [_writable_] vaultTokenAccount (optional)
 * @property [_writable_] destinationTokenAccount (optional)
 * @category Instructions
 * @category SpendingLimitUse
 * @category generated
 */
export type SpendingLimitUseInstructionAccounts = {
  multisig: web3.PublicKey
  member: web3.PublicKey
  spendingLimit: web3.PublicKey
  vault: web3.PublicKey
  destination: web3.PublicKey
  systemProgram?: web3.PublicKey
  mint?: web3.PublicKey
  vaultTokenAccount?: web3.PublicKey
  destinationTokenAccount?: web3.PublicKey
  tokenProgram?: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const spendingLimitUseInstructionDiscriminator = [
  16, 57, 130, 127, 193, 20, 155, 134,
]

/**
 * Creates a _SpendingLimitUse_ instruction.
 *
 * Optional accounts that are not provided default to the program ID since
 * this was indicated in the IDL from which this instruction was generated.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category SpendingLimitUse
 * @category generated
 */
export function createSpendingLimitUseInstruction(
  accounts: SpendingLimitUseInstructionAccounts,
  args: SpendingLimitUseInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = spendingLimitUseStruct.serialize({
    instructionDiscriminator: spendingLimitUseInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.member,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.spendingLimit,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.vault,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.destination,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.systemProgram ?? programId,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.mint ?? programId,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.vaultTokenAccount ?? programId,
      isWritable: accounts.vaultTokenAccount != null,
      isSigner: false,
    },
    {
      pubkey: accounts.destinationTokenAccount ?? programId,
      isWritable: accounts.destinationTokenAccount != null,
      isSigner: false,
    },
    {
      pubkey: accounts.tokenProgram ?? programId,
      isWritable: false,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
