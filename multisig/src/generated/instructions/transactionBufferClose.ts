/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'

/**
 * @category Instructions
 * @category TransactionBufferClose
 * @category generated
 */
export const transactionBufferCloseStruct = new beet.BeetArgsStruct<{
  instructionDiscriminator: number[] /* size: 8 */
}>(
  [['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)]],
  'TransactionBufferCloseInstructionArgs'
)
/**
 * Accounts required by the _transactionBufferClose_ instruction
 *
 * @property [] multisig
 * @property [_writable_] transactionBuffer
 * @property [**signer**] creator
 * @category Instructions
 * @category TransactionBufferClose
 * @category generated
 */
export type TransactionBufferCloseInstructionAccounts = {
  multisig: web3.PublicKey
  transactionBuffer: web3.<PERSON>Key
  creator: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const transactionBufferCloseInstructionDiscriminator = [
  17, 182, 208, 228, 136, 24, 178, 102,
]

/**
 * Creates a _TransactionBufferClose_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @category Instructions
 * @category TransactionBufferClose
 * @category generated
 */
export function createTransactionBufferCloseInstruction(
  accounts: TransactionBufferCloseInstructionAccounts,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = transactionBufferCloseStruct.serialize({
    instructionDiscriminator: transactionBufferCloseInstructionDiscriminator,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.transactionBuffer,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.creator,
      isWritable: false,
      isSigner: true,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
