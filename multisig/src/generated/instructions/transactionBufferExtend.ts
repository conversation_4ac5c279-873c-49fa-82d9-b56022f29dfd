/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  TransactionBufferExtendArgs,
  transactionBufferExtendArgsBeet,
} from '../types/TransactionBufferExtendArgs'

/**
 * @category Instructions
 * @category TransactionBufferExtend
 * @category generated
 */
export type TransactionBufferExtendInstructionArgs = {
  args: TransactionBufferExtendArgs
}
/**
 * @category Instructions
 * @category TransactionBufferExtend
 * @category generated
 */
export const transactionBufferExtendStruct = new beet.FixableBeetArgsStruct<
  TransactionBufferExtendInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', transactionBufferExtendArgsBeet],
  ],
  'TransactionBufferExtendInstructionArgs'
)
/**
 * Accounts required by the _transactionBufferExtend_ instruction
 *
 * @property [] multisig
 * @property [_writable_] transactionBuffer
 * @property [**signer**] creator
 * @category Instructions
 * @category TransactionBufferExtend
 * @category generated
 */
export type TransactionBufferExtendInstructionAccounts = {
  multisig: web3.PublicKey
  transactionBuffer: web3.PublicKey
  creator: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const transactionBufferExtendInstructionDiscriminator = [
  230, 157, 67, 56, 5, 238, 245, 146,
]

/**
 * Creates a _TransactionBufferExtend_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category TransactionBufferExtend
 * @category generated
 */
export function createTransactionBufferExtendInstruction(
  accounts: TransactionBufferExtendInstructionAccounts,
  args: TransactionBufferExtendInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = transactionBufferExtendStruct.serialize({
    instructionDiscriminator: transactionBufferExtendInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.transactionBuffer,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.creator,
      isWritable: false,
      isSigner: true,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
