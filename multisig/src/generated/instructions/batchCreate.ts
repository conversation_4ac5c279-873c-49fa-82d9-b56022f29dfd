/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import { BatchCreateArgs, batchCreateArgsBeet } from '../types/BatchCreateArgs'

/**
 * @category Instructions
 * @category BatchCreate
 * @category generated
 */
export type BatchCreateInstructionArgs = {
  args: BatchCreateArgs
}
/**
 * @category Instructions
 * @category BatchCreate
 * @category generated
 */
export const batchCreateStruct = new beet.FixableBeetArgsStruct<
  BatchCreateInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', batchCreateArgsBeet],
  ],
  'BatchCreateInstructionArgs'
)
/**
 * Accounts required by the _batchCreate_ instruction
 *
 * @property [_writable_] multisig
 * @property [_writable_] batch
 * @property [**signer**] creator
 * @property [_writable_, **signer**] rentPayer
 * @category Instructions
 * @category BatchCreate
 * @category generated
 */
export type BatchCreateInstructionAccounts = {
  multisig: web3.PublicKey
  batch: web3.PublicKey
  creator: web3.PublicKey
  rentPayer: web3.PublicKey
  systemProgram?: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const batchCreateInstructionDiscriminator = [
  194, 142, 141, 17, 55, 185, 20, 248,
]

/**
 * Creates a _BatchCreate_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category BatchCreate
 * @category generated
 */
export function createBatchCreateInstruction(
  accounts: BatchCreateInstructionAccounts,
  args: BatchCreateInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = batchCreateStruct.serialize({
    instructionDiscriminator: batchCreateInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.batch,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.creator,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.rentPayer,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
