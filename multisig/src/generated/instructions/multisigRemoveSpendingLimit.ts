/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  MultisigRemoveSpendingLimitArgs,
  multisigRemoveSpendingLimitArgsBeet,
} from '../types/MultisigRemoveSpendingLimitArgs'

/**
 * @category Instructions
 * @category MultisigRemoveSpendingLimit
 * @category generated
 */
export type MultisigRemoveSpendingLimitInstructionArgs = {
  args: MultisigRemoveSpendingLimitArgs
}
/**
 * @category Instructions
 * @category MultisigRemoveSpendingLimit
 * @category generated
 */
export const multisigRemoveSpendingLimitStruct = new beet.FixableBeetArgsStruct<
  MultisigRemoveSpendingLimitInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', multisigRemoveSpendingLimitArgsBeet],
  ],
  'MultisigRemoveSpendingLimitInstructionArgs'
)
/**
 * Accounts required by the _multisigRemoveSpendingLimit_ instruction
 *
 * @property [] multisig
 * @property [**signer**] configAuthority
 * @property [_writable_] spendingLimit
 * @property [_writable_] rentCollector
 * @category Instructions
 * @category MultisigRemoveSpendingLimit
 * @category generated
 */
export type MultisigRemoveSpendingLimitInstructionAccounts = {
  multisig: web3.PublicKey
  configAuthority: web3.PublicKey
  spendingLimit: web3.PublicKey
  rentCollector: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const multisigRemoveSpendingLimitInstructionDiscriminator = [
  228, 198, 136, 111, 123, 4, 178, 113,
]

/**
 * Creates a _MultisigRemoveSpendingLimit_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category MultisigRemoveSpendingLimit
 * @category generated
 */
export function createMultisigRemoveSpendingLimitInstruction(
  accounts: MultisigRemoveSpendingLimitInstructionAccounts,
  args: MultisigRemoveSpendingLimitInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = multisigRemoveSpendingLimitStruct.serialize({
    instructionDiscriminator:
      multisigRemoveSpendingLimitInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.configAuthority,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.spendingLimit,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.rentCollector,
      isWritable: true,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
