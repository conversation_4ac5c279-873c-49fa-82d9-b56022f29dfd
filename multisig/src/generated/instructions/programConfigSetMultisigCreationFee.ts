/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  ProgramConfigSetMultisigCreationFeeArgs,
  programConfigSetMultisigCreationFeeArgsBeet,
} from '../types/ProgramConfigSetMultisigCreationFeeArgs'

/**
 * @category Instructions
 * @category ProgramConfigSetMultisigCreationFee
 * @category generated
 */
export type ProgramConfigSetMultisigCreationFeeInstructionArgs = {
  args: ProgramConfigSetMultisigCreationFeeArgs
}
/**
 * @category Instructions
 * @category ProgramConfigSetMultisigCreationFee
 * @category generated
 */
export const programConfigSetMultisigCreationFeeStruct =
  new beet.BeetArgsStruct<
    ProgramConfigSetMultisigCreationFeeInstructionArgs & {
      instructionDiscriminator: number[] /* size: 8 */
    }
  >(
    [
      ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
      ['args', programConfigSetMultisigCreationFeeArgsBeet],
    ],
    'ProgramConfigSetMultisigCreationFeeInstructionArgs'
  )
/**
 * Accounts required by the _programConfigSetMultisigCreationFee_ instruction
 *
 * @property [_writable_] programConfig
 * @property [**signer**] authority
 * @category Instructions
 * @category ProgramConfigSetMultisigCreationFee
 * @category generated
 */
export type ProgramConfigSetMultisigCreationFeeInstructionAccounts = {
  programConfig: web3.PublicKey
  authority: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const programConfigSetMultisigCreationFeeInstructionDiscriminator = [
  101, 160, 249, 63, 154, 215, 153, 13,
]

/**
 * Creates a _ProgramConfigSetMultisigCreationFee_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category ProgramConfigSetMultisigCreationFee
 * @category generated
 */
export function createProgramConfigSetMultisigCreationFeeInstruction(
  accounts: ProgramConfigSetMultisigCreationFeeInstructionAccounts,
  args: ProgramConfigSetMultisigCreationFeeInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = programConfigSetMultisigCreationFeeStruct.serialize({
    instructionDiscriminator:
      programConfigSetMultisigCreationFeeInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.programConfig,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.authority,
      isWritable: false,
      isSigner: true,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
