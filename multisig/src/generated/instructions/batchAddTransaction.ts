/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  BatchAddTransactionArgs,
  batchAddTransactionArgsBeet,
} from '../types/BatchAddTransactionArgs'

/**
 * @category Instructions
 * @category BatchAddTransaction
 * @category generated
 */
export type BatchAddTransactionInstructionArgs = {
  args: BatchAddTransactionArgs
}
/**
 * @category Instructions
 * @category BatchAddTransaction
 * @category generated
 */
export const batchAddTransactionStruct = new beet.FixableBeetArgsStruct<
  BatchAddTransactionInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', batchAddTransactionArgsBeet],
  ],
  'BatchAddTransactionInstructionArgs'
)
/**
 * Accounts required by the _batchAddTransaction_ instruction
 *
 * @property [] multisig
 * @property [] proposal
 * @property [_writable_] batch
 * @property [_writable_] transaction
 * @property [**signer**] member
 * @property [_writable_, **signer**] rentPayer
 * @category Instructions
 * @category BatchAddTransaction
 * @category generated
 */
export type BatchAddTransactionInstructionAccounts = {
  multisig: web3.PublicKey
  proposal: web3.PublicKey
  batch: web3.PublicKey
  transaction: web3.PublicKey
  member: web3.PublicKey
  rentPayer: web3.PublicKey
  systemProgram?: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const batchAddTransactionInstructionDiscriminator = [
  89, 100, 224, 18, 69, 70, 54, 76,
]

/**
 * Creates a _BatchAddTransaction_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category BatchAddTransaction
 * @category generated
 */
export function createBatchAddTransactionInstruction(
  accounts: BatchAddTransactionInstructionAccounts,
  args: BatchAddTransactionInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = batchAddTransactionStruct.serialize({
    instructionDiscriminator: batchAddTransactionInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.proposal,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.batch,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.transaction,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.member,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.rentPayer,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
