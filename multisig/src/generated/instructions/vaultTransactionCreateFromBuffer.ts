/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  VaultTransactionCreateArgs,
  vaultTransactionCreateArgsBeet,
} from '../types/VaultTransactionCreateArgs'

/**
 * @category Instructions
 * @category VaultTransactionCreateFromBuffer
 * @category generated
 */
export type VaultTransactionCreateFromBufferInstructionArgs = {
  args: VaultTransactionCreateArgs
}
/**
 * @category Instructions
 * @category VaultTransactionCreateFromBuffer
 * @category generated
 */
export const vaultTransactionCreateFromBufferStruct =
  new beet.FixableBeetArgsStruct<
    VaultTransactionCreateFromBufferInstructionArgs & {
      instructionDiscriminator: number[] /* size: 8 */
    }
  >(
    [
      ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
      ['args', vaultTransactionCreateArgsBeet],
    ],
    'VaultTransactionCreateFromBufferInstructionArgs'
  )
/**
 * Accounts required by the _vaultTransactionCreateFromBuffer_ instruction
 *
 * @property [_writable_] vaultTransactionCreateItemMultisig
 * @property [_writable_] vaultTransactionCreateItemTransaction
 * @property [**signer**] vaultTransactionCreateItemCreator
 * @property [_writable_, **signer**] vaultTransactionCreateItemRentPayer
 * @property [] vaultTransactionCreateItemSystemProgram
 * @property [_writable_] transactionBuffer
 * @property [_writable_, **signer**] creator
 * @category Instructions
 * @category VaultTransactionCreateFromBuffer
 * @category generated
 */
export type VaultTransactionCreateFromBufferInstructionAccounts = {
  vaultTransactionCreateItemMultisig: web3.PublicKey
  vaultTransactionCreateItemTransaction: web3.PublicKey
  vaultTransactionCreateItemCreator: web3.PublicKey
  vaultTransactionCreateItemRentPayer: web3.PublicKey
  vaultTransactionCreateItemSystemProgram: web3.PublicKey
  transactionBuffer: web3.PublicKey
  creator: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const vaultTransactionCreateFromBufferInstructionDiscriminator = [
  222, 54, 149, 68, 87, 246, 48, 231,
]

/**
 * Creates a _VaultTransactionCreateFromBuffer_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category VaultTransactionCreateFromBuffer
 * @category generated
 */
export function createVaultTransactionCreateFromBufferInstruction(
  accounts: VaultTransactionCreateFromBufferInstructionAccounts,
  args: VaultTransactionCreateFromBufferInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = vaultTransactionCreateFromBufferStruct.serialize({
    instructionDiscriminator:
      vaultTransactionCreateFromBufferInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.vaultTransactionCreateItemMultisig,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.vaultTransactionCreateItemTransaction,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.vaultTransactionCreateItemCreator,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.vaultTransactionCreateItemRentPayer,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.vaultTransactionCreateItemSystemProgram,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.transactionBuffer,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.creator,
      isWritable: true,
      isSigner: true,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
