/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet'
import * as web3 from '@solana/web3.js'
import {
  ConfigTransactionCreateArgs,
  configTransactionCreateArgsBeet,
} from '../types/ConfigTransactionCreateArgs'

/**
 * @category Instructions
 * @category ConfigTransactionCreate
 * @category generated
 */
export type ConfigTransactionCreateInstructionArgs = {
  args: ConfigTransactionCreateArgs
}
/**
 * @category Instructions
 * @category ConfigTransactionCreate
 * @category generated
 */
export const configTransactionCreateStruct = new beet.FixableBeetArgsStruct<
  ConfigTransactionCreateInstructionArgs & {
    instructionDiscriminator: number[] /* size: 8 */
  }
>(
  [
    ['instructionDiscriminator', beet.uniformFixedSizeArray(beet.u8, 8)],
    ['args', configTransactionCreateArgsBeet],
  ],
  'ConfigTransactionCreateInstructionArgs'
)
/**
 * Accounts required by the _configTransactionCreate_ instruction
 *
 * @property [_writable_] multisig
 * @property [_writable_] transaction
 * @property [**signer**] creator
 * @property [_writable_, **signer**] rentPayer
 * @category Instructions
 * @category ConfigTransactionCreate
 * @category generated
 */
export type ConfigTransactionCreateInstructionAccounts = {
  multisig: web3.PublicKey
  transaction: web3.PublicKey
  creator: web3.PublicKey
  rentPayer: web3.PublicKey
  systemProgram?: web3.PublicKey
  anchorRemainingAccounts?: web3.AccountMeta[]
}

export const configTransactionCreateInstructionDiscriminator = [
  155, 236, 87, 228, 137, 75, 81, 39,
]

/**
 * Creates a _ConfigTransactionCreate_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category ConfigTransactionCreate
 * @category generated
 */
export function createConfigTransactionCreateInstruction(
  accounts: ConfigTransactionCreateInstructionAccounts,
  args: ConfigTransactionCreateInstructionArgs,
  programId = new web3.PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
) {
  const [data] = configTransactionCreateStruct.serialize({
    instructionDiscriminator: configTransactionCreateInstructionDiscriminator,
    ...args,
  })
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.multisig,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.transaction,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.creator,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.rentPayer,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
  ]

  if (accounts.anchorRemainingAccounts != null) {
    for (const acc of accounts.anchorRemainingAccounts) {
      keys.push(acc)
    }
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  })
  return ix
}
