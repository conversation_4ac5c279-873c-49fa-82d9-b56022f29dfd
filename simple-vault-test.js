const { Connection, PublicKey } = require('@solana/web3.js');
const multisig = require('@sqds/multisig');

// 配置
const MULTISIG_PROGRAM_ID = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
const MULTISIG_ADDRESS = "HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr";
const VAULT_ADDRESS = "EsvC81UWiKC3DPuqZwKYqYavqEg15U7ZUKLhrgNaa5Br";
const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

async function simpleVaultTest() {
  console.log('🔍 简化金库测试 - 只分析一个交易');
  console.log(`🏦 金库地址: ${VAULT_ADDRESS}`);
  console.log('=' .repeat(60));
  
  try {
    const vaultPubkey = new PublicKey(VAULT_ADDRESS);
    
    // 获取金库的签名历史
    console.log('📅 获取金库签名历史...');
    const signatures = await connection.getSignaturesForAddress(vaultPubkey, { limit: 5 });
    
    console.log(`✅ 找到 ${signatures.length} 个签名`);
    
    if (signatures.length > 0) {
      // 只分析最新的一个交易
      const latestSig = signatures[0];
      console.log(`\n🔍 分析最新交易: ${latestSig.signature}`);
      console.log(`📅 时间: ${new Date(latestSig.blockTime * 1000).toISOString()}`);
      
      try {
        const txInfo = await connection.getTransaction(latestSig.signature, {
          maxSupportedTransactionVersion: 0
        });
        
        if (txInfo && txInfo.transaction && txInfo.transaction.message) {
          const message = txInfo.transaction.message;
          console.log(`✅ 获取到交易详情`);
          console.log(`  - 指令数量: ${message.instructions.length}`);
          console.log(`  - 账户数量: ${message.accountKeys.length}`);
          console.log(`  - 费用: ${txInfo.meta?.fee || '未知'}`);
          console.log(`  - 状态: ${txInfo.meta?.err ? '失败' : '成功'}`);
          
          // 显示前几个账户
          console.log(`\n📋 涉及的账户:`);
          message.accountKeys.slice(0, 8).forEach((key, idx) => {
            let description = '';
            if (key.toBase58() === VAULT_ADDRESS) {
              description = ' 🏦 (金库地址)';
            } else if (key.toBase58() === MULTISIG_ADDRESS) {
              description = ' 🔐 (多签地址)';
            } else if (key.toBase58() === '11111111111111111111111111111112') {
              description = ' ⚙️ (系统程序)';
            } else if (key.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
              description = ' 🪙 (Token程序)';
            } else if (key.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
              description = ' 🔧 (Squads程序)';
            }
            console.log(`  ${idx}: ${key.toBase58()}${description}`);
          });
          
          // 分析指令
          console.log(`\n🔧 指令分析:`);
          message.instructions.forEach((instruction, idx) => {
            const programId = message.accountKeys[instruction.programIdIndex];
            console.log(`\n  指令 ${idx + 1}:`);
            console.log(`    - 程序ID: ${programId.toBase58()}`);
            console.log(`    - 账户索引: [${instruction.accounts.join(', ')}]`);
            console.log(`    - 数据长度: ${instruction.data.length} bytes`);
            
            // 重点分析转账指令
            if (programId.toBase58() === '11111111111111111111111111111112') {
              console.log(`    - 🎯 系统程序指令（可能是SOL转账）`);
              
              if (instruction.data && instruction.data.length >= 12) {
                try {
                  const dataView = new DataView(instruction.data.buffer);
                  const instructionType = dataView.getUint32(0, true);
                  console.log(`    - 指令类型: ${instructionType}`);
                  
                  if (instructionType === 2) { // Transfer
                    const lamports = dataView.getBigUint64(4, true);
                    const sol = Number(lamports) / **********;
                    
                    const fromAccount = message.accountKeys[instruction.accounts[0]];
                    const toAccount = message.accountKeys[instruction.accounts[1]];
                    
                    console.log(`    - 🎉 发现SOL转账！`);
                    console.log(`      💰 金额: ${sol} SOL`);
                    console.log(`      📤 从: ${fromAccount.toBase58()}`);
                    console.log(`      📥 到: ${toAccount.toBase58()}`);
                    
                    // 检查是否是从金库发出
                    if (fromAccount.toBase58() === VAULT_ADDRESS) {
                      console.log(`      🏦 ✅ 这是从金库发出的转账！`);
                    }
                    
                    // 这就是我们要的数据！
                    console.log(`\n🎯 找到转账数据:`);
                    console.log(`  - 交易类型: SOL转账`);
                    console.log(`  - 转账金额: ${sol}`);
                    console.log(`  - 转账Token: SOL`);
                    console.log(`  - 目标地址: ${toAccount.toBase58()}`);
                    console.log(`  - 创建时间: ${new Date(latestSig.blockTime * 1000).toISOString()}`);
                    console.log(`  - 交易签名: ${latestSig.signature}`);
                    
                  } else if (instructionType === 0) {
                    console.log(`    - 这是创建账户指令`);
                  } else if (instructionType === 1) {
                    console.log(`    - 这是分配指令`);
                  } else {
                    console.log(`    - 未知的系统指令类型: ${instructionType}`);
                  }
                } catch (parseErr) {
                  console.log(`    - ❌ 解析失败: ${parseErr.message}`);
                }
              } else {
                console.log(`    - 数据长度不足，无法解析转账`);
              }
            }
            
            else if (programId.toBase58() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
              console.log(`    - 🎯 Token程序指令`);
              
              if (instruction.data && instruction.data.length > 0) {
                const tokenInstructionType = instruction.data[0];
                console.log(`    - Token指令类型: ${tokenInstructionType}`);
                
                if (tokenInstructionType === 3) { // Transfer
                  console.log(`    - 🎉 发现Token转账！`);
                  
                  if (instruction.data.length >= 9) {
                    try {
                      const dataView = new DataView(instruction.data.buffer);
                      const amount = dataView.getBigUint64(1, true);
                      
                      const sourceAccount = message.accountKeys[instruction.accounts[0]];
                      const destAccount = message.accountKeys[instruction.accounts[1]];
                      
                      console.log(`      🪙 金额: ${amount} (原始单位)`);
                      console.log(`      📤 从: ${sourceAccount.toBase58()}`);
                      console.log(`      📥 到: ${destAccount.toBase58()}`);
                      
                      console.log(`\n🎯 找到Token转账数据:`);
                      console.log(`  - 交易类型: Token转账`);
                      console.log(`  - 转账金额: ${amount}`);
                      console.log(`  - 转账Token: 未知Token`);
                      console.log(`  - 目标地址: ${destAccount.toBase58()}`);
                      console.log(`  - 创建时间: ${new Date(latestSig.blockTime * 1000).toISOString()}`);
                      
                    } catch (parseErr) {
                      console.log(`    - ❌ Token解析失败: ${parseErr.message}`);
                    }
                  }
                }
              }
            }
            
            else if (programId.toBase58() === MULTISIG_PROGRAM_ID.toBase58()) {
              console.log(`    - 🔧 Squads多签程序指令`);
            }
            
            else {
              console.log(`    - 🔍 其他程序: ${programId.toBase58().slice(0, 8)}...`);
            }
          });
          
        } else {
          console.log('❌ 无法获取交易详情');
        }
        
      } catch (txErr) {
        console.log(`❌ 获取交易失败: ${txErr.message}`);
      }
      
    } else {
      console.log('❌ 没有找到签名');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

simpleVaultTest().then(() => {
  console.log('\n🎉 简化金库测试完成');
  process.exit(0);
}).catch(err => {
  console.error('💥 测试异常:', err);
  process.exit(1);
});
